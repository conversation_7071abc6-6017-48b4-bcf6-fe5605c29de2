# Gallery Tuner Album Functionality - Issue Resolution

## Problem Identified

The album functionality was failing with a **403 Forbidden** error when trying to access the Google Photos Albums API. The error occurred because the OAuth scopes were insufficient for album management operations.

### Error Details
```
System.Net.Http.HttpRequestException: Response status code does not indicate success: 403 (Forbidden).
   at VidCompressor.Services.GooglePhotosService.FindGalleryTunerAlbumAsync(String accessToken)
```

## Root Cause

The application was requesting these OAuth scopes:
- `https://www.googleapis.com/auth/photoslibrary.appendonly` (upload only)
- `https://www.googleapis.com/auth/photospicker.mediaitems.readonly` (picker read-only)

However, album management (list, create, add items to albums) requires the full:
- `https://www.googleapis.com/auth/photoslibrary` scope

## Solution Applied

### 1. Updated OAuth Scopes

**Files Modified:**
- `backend/Controllers/AuthController.cs` (line 43)
- `backend/Controllers/VideosController.cs` (line 822)

**Change:**
```csharp
// Before
var scopes = "openid email profile https://www.googleapis.com/auth/photoslibrary.appendonly https://www.googleapis.com/auth/photospicker.mediaitems.readonly";

// After  
var scopes = "openid email profile https://www.googleapis.com/auth/photoslibrary https://www.googleapis.com/auth/photospicker.mediaitems.readonly";
```

### 2. Enhanced Error Handling

**File Modified:** `shared/Services/GooglePhotosService.cs`

Added detailed error handling for 403 Forbidden responses in:
- `FindGalleryTunerAlbumAsync()` method
- `CreateGalleryTunerAlbumAsync()` method

The enhanced error handling:
- Detects 403 Forbidden responses
- Logs specific error messages about missing scopes
- Throws `UnauthorizedAccessException` with clear user-facing messages
- Provides guidance for re-authentication

## Required User Action

**IMPORTANT:** Existing users will need to re-authenticate to get the new permissions.

### For Testing:
1. Clear any existing authentication tokens
2. Sign out and sign back in through the application
3. Grant the new permissions when prompted by Google
4. Test the compression and upload workflow

### What Users Will See:
When Google prompts for permissions, users will now see:
- **Previous:** "Upload photos and videos to Google Photos"
- **New:** "View and manage your Google Photos library"

This broader permission is required to create and manage the "Gallery Tuner" album.

## Verification Steps

1. **Authentication:** Ensure users can successfully authenticate with new scopes
2. **Album Creation:** Verify that the "Gallery Tuner" album is created on first upload
3. **Album Reuse:** Confirm that subsequent uploads use the existing album
4. **Media Addition:** Check that uploaded items appear in the Gallery Tuner album
5. **Error Handling:** Verify graceful handling if album operations fail

## Technical Notes

- The `photoslibrary` scope includes all capabilities of `photoslibrary.appendonly`
- Album operations are treated as "bonus features" - if they fail, uploads still succeed
- The system uses case-insensitive search to find existing "Gallery Tuner" albums
- Batch operations are used for efficiency when adding multiple items to albums

## Monitoring

Watch for these log messages to confirm proper operation:
- `"Ensuring Gallery Tuner album exists"`
- `"Found existing Gallery Tuner album with ID: {AlbumId}"`
- `"Successfully created Gallery Tuner album with ID: {AlbumId}"`
- `"Successfully added {Count} media items to Gallery Tuner album"`

If you see permission errors, users need to re-authenticate with the updated scopes.
