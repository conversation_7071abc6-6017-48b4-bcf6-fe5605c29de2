# Gallery Tuner Album Functionality - Updated Implementation

## Google Photos API Changes (March 2025)

Google announced significant changes to the Google Photos Library API that affect our album functionality:

### Key Changes:
1. **Scope Removal**: The broad `photoslibrary` scope is being removed
2. **Limited Access**: Apps can only access albums and media items they created
3. **No Global Listing**: Cannot list or search all user albums anymore

### Impact on Our Implementation:
- **Cannot find existing "Gallery Tuner" albums** created by other apps or manually by users
- **Must create new albums** for each compression session
- **Can only manage app-created content**

## Updated Approach

Instead of trying to find/reuse existing albums, we now:
1. **Create a unique album** for each compression session
2. **Use timestamped names** like "Gallery Tuner - 2024-12-27 14:30"
3. **Only use app-created data scopes**

## Solution Applied

### 1. Updated OAuth Scopes (Future-Proof)

**Files Modified:**
- `backend/Controllers/AuthController.cs` (line 43)
- `backend/Controllers/VideosController.cs` (line 822)

**Updated Scopes:**
```csharp
var scopes = "openid email profile " +
    "https://www.googleapis.com/auth/photoslibrary.appendonly " +
    "https://www.googleapis.com/auth/photospicker.mediaitems.readonly " +
    "https://www.googleapis.com/auth/photoslibrary.readonly.appcreateddata " +
    "https://www.googleapis.com/auth/photoslibrary.edit.appcreateddata";
```

**Scope Breakdown:**
- `photoslibrary.appendonly` - Upload photos/videos and create albums
- `photospicker.mediaitems.readonly` - Access Photos Picker API
- `photoslibrary.readonly.appcreateddata` - Read app-created albums/media
- `photoslibrary.edit.appcreateddata` - Edit app-created albums (add/remove items)

### 2. Simplified Album Management

**File Modified:** `shared/Services/GooglePhotosService.cs`

**Key Changes:**
- **Removed** `FindGalleryTunerAlbumAsync()` - Can't list all albums anymore
- **Updated** `EnsureGalleryTunerAlbumAsync()` - Now always creates new albums
- **Modified** `CreateGalleryTunerAlbumAsync()` - Accepts custom album titles
- **Added** timestamp-based album naming for uniqueness

**New Album Naming:**
- Format: `"Gallery Tuner - YYYY-MM-DD HH:MM"`
- Example: `"Gallery Tuner - 2024-12-27 14:30"`
- Ensures each compression session gets its own album

## Required User Action

**IMPORTANT:** Existing users will need to re-authenticate to get the new permissions.

### For Testing:
1. Clear any existing authentication tokens
2. Sign out and sign back in through the application
3. Grant the new permissions when prompted by Google
4. Test the compression and upload workflow

### What Users Will See:
When Google prompts for permissions, users will now see:
- **Previous:** "Upload photos and videos to Google Photos"
- **New:** "View and manage your Google Photos library"

This broader permission is required to create and manage the "Gallery Tuner" album.

## Verification Steps

1. **Authentication:** Ensure users can successfully authenticate with new scopes
2. **Album Creation:** Verify that the "Gallery Tuner" album is created on first upload
3. **Album Reuse:** Confirm that subsequent uploads use the existing album
4. **Media Addition:** Check that uploaded items appear in the Gallery Tuner album
5. **Error Handling:** Verify graceful handling if album operations fail

## Technical Notes

- The `photoslibrary` scope includes all capabilities of `photoslibrary.appendonly`
- Album operations are treated as "bonus features" - if they fail, uploads still succeed
- The system uses case-insensitive search to find existing "Gallery Tuner" albums
- Batch operations are used for efficiency when adding multiple items to albums

## Monitoring

Watch for these log messages to confirm proper operation:
- `"Ensuring Gallery Tuner album exists"`
- `"Found existing Gallery Tuner album with ID: {AlbumId}"`
- `"Successfully created Gallery Tuner album with ID: {AlbumId}"`
- `"Successfully added {Count} media items to Gallery Tuner album"`

If you see permission errors, users need to re-authenticate with the updated scopes.
