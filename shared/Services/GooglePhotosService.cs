using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;
using System.Text.Json;
using System.Text.Json.Serialization;
using Polly;

namespace VidCompressor.Services;

public class GooglePhotosService
{
    private readonly HttpClient _httpClient;
    private readonly ILogger<GooglePhotosService> _logger;
    private readonly ResiliencePipeline<HttpResponseMessage> _retryPipeline;

    public GooglePhotosService(IHttpClientFactory httpClientFactory, ILogger<GooglePhotosService> logger)
    {
        _httpClient = httpClientFactory.CreateClient();
        _logger = logger;

        // Create retry pipeline for rate limiting (429) and server errors (5xx)
        _retryPipeline = new ResiliencePipelineBuilder<HttpResponseMessage>()
            .AddRetry(new Polly.Retry.RetryStrategyOptions<HttpResponseMessage>
            {
                ShouldHandle = new PredicateBuilder<HttpResponseMessage>()
                    .HandleResult(r => !r.IsSuccessStatusCode &&
                        (r.StatusCode == System.Net.HttpStatusCode.TooManyRequests ||
                         (int)r.StatusCode >= 500)),
                MaxRetryAttempts = 5,
                Delay = TimeSpan.FromSeconds(1),
                BackoffType = Polly.DelayBackoffType.Exponential,
                UseJitter = true,
                OnRetry = args =>
                {
                    _logger.LogWarning("Google Photos API request failed (attempt {RetryCount}/5). Status: {StatusCode}. Retrying in {Delay}ms",
                        args.AttemptNumber, args.Outcome.Result?.StatusCode, args.RetryDelay.TotalMilliseconds);
                    return ValueTask.CompletedTask;
                }
            })
            .Build();
    }

    public async Task<List<string>> GetVideosAsync(string accessToken)
    {
        var request = new HttpRequestMessage(HttpMethod.Get, "https://photoslibrary.googleapis.com/v1/mediaItems");
        request.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

        var response = await _httpClient.SendAsync(request);
        response.EnsureSuccessStatusCode();

        var content = await response.Content.ReadAsStringAsync();
        var mediaResponse = JsonSerializer.Deserialize<MediaItemsResponse>(content);

        var videos = new List<string>();
        if (mediaResponse?.MediaItems != null)
        {
            foreach (var item in mediaResponse.MediaItems)
            {
                if (item.MediaMetadata?.Video != null)
                {
                    videos.Add(item.Id);
                }
            }
        }

        return videos;
    }

    public async Task<VideoInfo> GetVideoInfoAsync(string accessToken, string mediaItemId)
    {
        var request = new HttpRequestMessage(HttpMethod.Get, $"https://photoslibrary.googleapis.com/v1/mediaItems/{mediaItemId}");
        request.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

        var response = await _httpClient.SendAsync(request);
        response.EnsureSuccessStatusCode();

        var content = await response.Content.ReadAsStringAsync();
        var mediaItem = JsonSerializer.Deserialize<MediaItem>(content);

        if (mediaItem == null)
        {
            throw new InvalidOperationException($"Media item with ID {mediaItemId} not found");
        }

        var videoMetadata = mediaItem.MediaMetadata?.Video;
        if (videoMetadata == null)
        {
            throw new InvalidOperationException($"Media item {mediaItemId} is not a video");
        }

        // Calculate file size estimate
        var width = int.Parse(mediaItem.MediaMetadata?.Width ?? "1920");
        var height = int.Parse(mediaItem.MediaMetadata?.Height ?? "1080");
        var fps = videoMetadata.Fps > 0 ? videoMetadata.Fps : 30.0;

        var durationSeconds = 60.0; // Default estimate
        var estimatedBitrate = (width * height * fps) / 250000.0;
        var estimatedSizeBytes = (long)(estimatedBitrate * durationSeconds * 125000);

        return new VideoInfo
        {
            MediaItemId = mediaItemId,
            Width = width,
            Height = height,
            Duration = durationSeconds,
            EstimatedSizeBytes = estimatedSizeBytes,
            Filename = mediaItem.Filename ?? "video.mp4"
        };
    }

    public async Task<Stream> DownloadVideoAsync(string accessToken, string mediaItemId, string? baseUrl = null)
    {
        _logger.LogInformation("Downloading video {MediaItemId}", mediaItemId);
        _logger.LogInformation("Using access token: {TokenPrefix}...", accessToken?.Substring(0, Math.Min(10, accessToken?.Length ?? 0)));

        string mediaBaseUrl;

        // If baseUrl is provided (from PhotosPicker API), use it directly
        if (!string.IsNullOrEmpty(baseUrl))
        {
            _logger.LogInformation("Using provided baseUrl from PhotosPicker API");
            mediaBaseUrl = baseUrl;
        }
        else
        {
            _logger.LogInformation("No baseUrl provided, falling back to Photos Library API (may fail for user-selected media)");

            // Fallback to Photos Library API (this will likely fail for user-selected media due to scope restrictions)
            var request = new HttpRequestMessage(HttpMethod.Get, $"https://photoslibrary.googleapis.com/v1/mediaItems/{mediaItemId}");
            request.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

            var response = await _httpClient.SendAsync(request);

            if (!response.IsSuccessStatusCode)
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogError("Failed to get media item. Status: {StatusCode}, Content: {Content}",
                    response.StatusCode, errorContent);
                throw new HttpRequestException($"Failed to get media item: {response.StatusCode} - {errorContent}");
            }

            var content = await response.Content.ReadAsStringAsync();
            var mediaItem = JsonSerializer.Deserialize<MediaItem>(content);

            if (mediaItem == null)
            {
                throw new InvalidOperationException($"Media item with ID {mediaItemId} not found");
            }

            if (string.IsNullOrEmpty(mediaItem.BaseUrl))
            {
                throw new InvalidOperationException($"No base URL available for media item {mediaItemId}");
            }

            mediaBaseUrl = mediaItem.BaseUrl;
        }

        // Download the video using the base URL with video parameters
        var downloadUrl = $"{mediaBaseUrl}=dv"; // =dv parameter for video download
        var downloadRequest = new HttpRequestMessage(HttpMethod.Get, downloadUrl);
        downloadRequest.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

        var downloadResponse = await _httpClient.SendAsync(downloadRequest);
        downloadResponse.EnsureSuccessStatusCode();

        _logger.LogInformation("Successfully downloaded video {MediaItemId}, size: {Size} bytes",
            mediaItemId, downloadResponse.Content.Headers.ContentLength);

        return await downloadResponse.Content.ReadAsStreamAsync();
    }

    public async Task<Stream> DownloadPhotoAsync(string accessToken, string mediaItemId, string? baseUrl = null)
    {
        _logger.LogInformation("Downloading photo {MediaItemId}", mediaItemId);
        _logger.LogInformation("Using access token: {TokenPrefix}...", accessToken?.Substring(0, Math.Min(10, accessToken?.Length ?? 0)));

        string mediaBaseUrl;

        // If baseUrl is provided (from PhotosPicker API), use it directly
        if (!string.IsNullOrEmpty(baseUrl))
        {
            _logger.LogInformation("Using provided baseUrl from PhotosPicker API");
            mediaBaseUrl = baseUrl;
        }
        else
        {
            _logger.LogInformation("No baseUrl provided, falling back to Photos Library API (may fail for user-selected media)");

            // Fallback to Photos Library API (this will likely fail for user-selected media due to scope restrictions)
            var request = new HttpRequestMessage(HttpMethod.Get, $"https://photoslibrary.googleapis.com/v1/mediaItems/{mediaItemId}");
            request.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

            var response = await _httpClient.SendAsync(request);

            if (!response.IsSuccessStatusCode)
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogError("Failed to get media item. Status: {StatusCode}, Content: {Content}",
                    response.StatusCode, errorContent);
                throw new HttpRequestException($"Failed to get media item: {response.StatusCode} - {errorContent}");
            }

            var content = await response.Content.ReadAsStringAsync();
            var mediaItem = JsonSerializer.Deserialize<MediaItem>(content);

            if (mediaItem == null)
            {
                throw new InvalidOperationException($"Media item with ID {mediaItemId} not found");
            }

            if (string.IsNullOrEmpty(mediaItem.BaseUrl))
            {
                throw new InvalidOperationException($"No base URL available for media item {mediaItemId}");
            }

            mediaBaseUrl = mediaItem.BaseUrl;
        }

        // Download the photo using the base URL with photo parameters
        var downloadUrl = $"{mediaBaseUrl}=d"; // =d parameter for photo download (original quality)
        var downloadRequest = new HttpRequestMessage(HttpMethod.Get, downloadUrl);
        downloadRequest.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

        var downloadResponse = await _httpClient.SendAsync(downloadRequest);
        downloadResponse.EnsureSuccessStatusCode();

        _logger.LogInformation("Successfully downloaded photo {MediaItemId}, size: {Size} bytes",
            mediaItemId, downloadResponse.Content.Headers.ContentLength);

        return await downloadResponse.Content.ReadAsStreamAsync();
    }

    public async Task UploadVideoAsync(string accessToken, string filePath)
    {
        _logger.LogInformation("Uploading video from {FilePath}", filePath);

        // Step 1: Upload the file to get an upload token
        var uploadRequest = new HttpRequestMessage(HttpMethod.Post, "https://photoslibrary.googleapis.com/v1/uploads");
        uploadRequest.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
        uploadRequest.Headers.Add("X-Goog-Upload-File-Name", Path.GetFileName(filePath));
        uploadRequest.Headers.Add("X-Goog-Upload-Protocol", "raw");

        var fileBytes = await File.ReadAllBytesAsync(filePath);
        uploadRequest.Content = new ByteArrayContent(fileBytes);
        uploadRequest.Content.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue("application/octet-stream");

        var uploadResponse = await _retryPipeline.ExecuteAsync(async _ => await _httpClient.SendAsync(uploadRequest));
        uploadResponse.EnsureSuccessStatusCode();

        var uploadToken = await uploadResponse.Content.ReadAsStringAsync();

        // Step 2: Create the media item using the upload token
        var createRequest = new HttpRequestMessage(HttpMethod.Post, "https://photoslibrary.googleapis.com/v1/mediaItems:batchCreate");
        createRequest.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
        createRequest.Content = new StringContent(JsonSerializer.Serialize(new
        {
            newMediaItems = new[]
            {
                new
                {
                    description = "Compressed video",
                    simpleMediaItem = new
                    {
                        uploadToken = uploadToken
                    }
                }
            }
        }), System.Text.Encoding.UTF8, "application/json");

        var createResponse = await _retryPipeline.ExecuteAsync(async _ => await _httpClient.SendAsync(createRequest));
        createResponse.EnsureSuccessStatusCode();

        // Parse response to get the created media item ID and add to Gallery Tuner album
        var responseContent = await createResponse.Content.ReadAsStringAsync();
        var batchCreateResponse = JsonSerializer.Deserialize<BatchCreateResponse>(responseContent);

        if (batchCreateResponse?.NewMediaItemResults?.FirstOrDefault()?.MediaItem?.Id is string mediaItemId && !string.IsNullOrEmpty(mediaItemId))
        {
            try
            {
                var albumId = await EnsureGalleryTunerAlbumAsync(accessToken);
                await AddMediaItemsToAlbumAsync(accessToken, albumId, new[] { mediaItemId });
                _logger.LogInformation("Successfully added uploaded video to Gallery Tuner album");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to add uploaded video to Gallery Tuner album, but upload was successful");
            }
        }

        _logger.LogInformation("Successfully uploaded video from {FilePath}", filePath);
    }

    public async Task UploadPhotoAsync(string accessToken, string filePath)
    {
        _logger.LogInformation("Uploading photo from {FilePath}", filePath);

        // Step 1: Upload the file to get an upload token
        var uploadRequest = new HttpRequestMessage(HttpMethod.Post, "https://photoslibrary.googleapis.com/v1/uploads");
        uploadRequest.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
        uploadRequest.Headers.Add("X-Goog-Upload-File-Name", Path.GetFileName(filePath));
        uploadRequest.Headers.Add("X-Goog-Upload-Protocol", "raw");

        var fileBytes = await File.ReadAllBytesAsync(filePath);
        uploadRequest.Content = new ByteArrayContent(fileBytes);
        uploadRequest.Content.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue("application/octet-stream");

        var uploadResponse = await _retryPipeline.ExecuteAsync(async _ => await _httpClient.SendAsync(uploadRequest));
        uploadResponse.EnsureSuccessStatusCode();

        var uploadToken = await uploadResponse.Content.ReadAsStringAsync();

        // Step 2: Create the media item using the upload token
        var createRequest = new HttpRequestMessage(HttpMethod.Post, "https://photoslibrary.googleapis.com/v1/mediaItems:batchCreate");
        createRequest.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
        createRequest.Content = new StringContent(JsonSerializer.Serialize(new
        {
            newMediaItems = new[]
            {
                new
                {
                    description = "Compressed photo",
                    simpleMediaItem = new
                    {
                        uploadToken = uploadToken
                    }
                }
            }
        }), System.Text.Encoding.UTF8, "application/json");

        var createResponse = await _retryPipeline.ExecuteAsync(async _ => await _httpClient.SendAsync(createRequest));
        createResponse.EnsureSuccessStatusCode();

        // Parse response to get the created media item ID and add to Gallery Tuner album
        var responseContent = await createResponse.Content.ReadAsStringAsync();
        var batchCreateResponse = JsonSerializer.Deserialize<BatchCreateResponse>(responseContent);

        if (batchCreateResponse?.NewMediaItemResults?.FirstOrDefault()?.MediaItem?.Id is string mediaItemId && !string.IsNullOrEmpty(mediaItemId))
        {
            try
            {
                var albumId = await EnsureGalleryTunerAlbumAsync(accessToken);
                await AddMediaItemsToAlbumAsync(accessToken, albumId, new[] { mediaItemId });
                _logger.LogInformation("Successfully added uploaded photo to Gallery Tuner album");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to add uploaded photo to Gallery Tuner album, but upload was successful");
            }
        }

        _logger.LogInformation("Successfully uploaded photo from {FilePath}", filePath);
    }

    /// <summary>
    /// Find or create the 'Gallery Tuner' album
    /// </summary>
    /// <returns>Album ID of the Gallery Tuner album</returns>
    public async Task<string> EnsureGalleryTunerAlbumAsync(string accessToken)
    {
        _logger.LogInformation("Ensuring Gallery Tuner album exists");

        // First, try to find existing album
        var albumId = await FindGalleryTunerAlbumAsync(accessToken);
        if (!string.IsNullOrEmpty(albumId))
        {
            _logger.LogInformation("Found existing Gallery Tuner album with ID: {AlbumId}", albumId);
            return albumId;
        }

        // Album doesn't exist, create it
        _logger.LogInformation("Gallery Tuner album not found, creating new album");
        return await CreateGalleryTunerAlbumAsync(accessToken);
    }

    /// <summary>
    /// Find the 'Gallery Tuner' album if it exists
    /// </summary>
    /// <returns>Album ID if found, null otherwise</returns>
    private async Task<string?> FindGalleryTunerAlbumAsync(string accessToken)
    {
        var request = new HttpRequestMessage(HttpMethod.Get, "https://photoslibrary.googleapis.com/v1/albums?pageSize=50");
        request.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

        var response = await _retryPipeline.ExecuteAsync(async _ => await _httpClient.SendAsync(request));
        response.EnsureSuccessStatusCode();

        var content = await response.Content.ReadAsStringAsync();
        var albumsResponse = JsonSerializer.Deserialize<ListAlbumsResponse>(content);

        var galleryTunerAlbum = albumsResponse?.Albums?.FirstOrDefault(a =>
            string.Equals(a.Title, "Gallery Tuner", StringComparison.OrdinalIgnoreCase));

        return galleryTunerAlbum?.Id;
    }

    /// <summary>
    /// Create the 'Gallery Tuner' album
    /// </summary>
    /// <returns>Album ID of the created album</returns>
    private async Task<string> CreateGalleryTunerAlbumAsync(string accessToken)
    {
        var createRequest = new HttpRequestMessage(HttpMethod.Post, "https://photoslibrary.googleapis.com/v1/albums");
        createRequest.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

        var albumRequest = new CreateAlbumRequest
        {
            Album = new AlbumDetails
            {
                Title = "Gallery Tuner"
            }
        };

        createRequest.Content = new StringContent(
            JsonSerializer.Serialize(albumRequest),
            System.Text.Encoding.UTF8,
            "application/json");

        var createResponse = await _retryPipeline.ExecuteAsync(async _ => await _httpClient.SendAsync(createRequest));
        createResponse.EnsureSuccessStatusCode();

        var responseContent = await createResponse.Content.ReadAsStringAsync();
        var createdAlbum = JsonSerializer.Deserialize<Album>(responseContent);

        if (createdAlbum == null || string.IsNullOrEmpty(createdAlbum.Id))
        {
            throw new InvalidOperationException("Failed to create Gallery Tuner album - no album ID returned");
        }

        _logger.LogInformation("Successfully created Gallery Tuner album with ID: {AlbumId}", createdAlbum.Id);
        return createdAlbum.Id;
    }

    /// <summary>
    /// Add media items to the Gallery Tuner album
    /// </summary>
    /// <param name="accessToken">Google Photos access token</param>
    /// <param name="albumId">Album ID to add items to</param>
    /// <param name="mediaItemIds">List of media item IDs to add</param>
    public async Task AddMediaItemsToAlbumAsync(string accessToken, string albumId, IEnumerable<string> mediaItemIds)
    {
        var mediaItemIdsList = mediaItemIds.ToList();
        if (!mediaItemIdsList.Any())
        {
            _logger.LogInformation("No media items to add to album");
            return;
        }

        _logger.LogInformation("Adding {Count} media items to album {AlbumId}", mediaItemIdsList.Count, albumId);

        var addRequest = new HttpRequestMessage(HttpMethod.Post, $"https://photoslibrary.googleapis.com/v1/albums/{albumId}:batchAddMediaItems");
        addRequest.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

        var batchAddRequest = new BatchAddMediaItemsRequest
        {
            MediaItemIds = mediaItemIdsList
        };

        addRequest.Content = new StringContent(
            JsonSerializer.Serialize(batchAddRequest),
            System.Text.Encoding.UTF8,
            "application/json");

        var addResponse = await _retryPipeline.ExecuteAsync(async _ => await _httpClient.SendAsync(addRequest));
        addResponse.EnsureSuccessStatusCode();

        _logger.LogInformation("Successfully added {Count} media items to Gallery Tuner album", mediaItemIdsList.Count);
    }

    /// <summary>
    /// Batch upload multiple media items to Google Photos
    /// </summary>
    /// <returns>Dictionary mapping job IDs to their Google Photos URLs</returns>
    public async Task<Dictionary<string, string>> BatchUploadAsync(string accessToken, IEnumerable<BatchUploadItem> jobs, CancellationToken cancellationToken = default)
    {
        var jobList = jobs.ToList();
        if (!jobList.Any())
        {
            _logger.LogInformation("No jobs provided for batch upload");
            return new Dictionary<string, string>();
        }

        _logger.LogInformation("Starting batch upload of {JobCount} items", jobList.Count);

        var uploadTokens = new List<(string JobId, string UploadToken, string Description)>();

        // Step 1: Upload all files to get upload tokens
        foreach (var job in jobList)
        {
            try
            {
                var filePath = job.CompressedFilePath;
                if (string.IsNullOrEmpty(filePath) || !File.Exists(filePath))
                {
                    _logger.LogWarning("Compressed file not found for job {JobId}: {FilePath}", job.Id, filePath);
                    continue;
                }

                var uploadRequest = new HttpRequestMessage(HttpMethod.Post, "https://photoslibrary.googleapis.com/v1/uploads");
                uploadRequest.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
                uploadRequest.Headers.Add("X-Goog-Upload-File-Name", Path.GetFileName(filePath));
                uploadRequest.Headers.Add("X-Goog-Upload-Protocol", "raw");

                var fileBytes = await File.ReadAllBytesAsync(filePath, cancellationToken);
                uploadRequest.Content = new ByteArrayContent(fileBytes);
                uploadRequest.Content.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue("application/octet-stream");

                var uploadResponse = await _retryPipeline.ExecuteAsync(async _ => await _httpClient.SendAsync(uploadRequest, cancellationToken));
                uploadResponse.EnsureSuccessStatusCode();

                var uploadToken = await uploadResponse.Content.ReadAsStringAsync(cancellationToken);
                var description = job.MediaType == 0 ? "Compressed photo" : "Compressed video"; // 0 = Photo, 1 = Video

                uploadTokens.Add((job.Id, uploadToken, description));
                _logger.LogInformation("Successfully uploaded file for job {JobId}, got upload token", job.Id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to upload file for job {JobId}", job.Id);
                throw; // Re-throw to fail the entire batch
            }
        }

        if (!uploadTokens.Any())
        {
            _logger.LogWarning("No upload tokens obtained, skipping batch create");
            return new Dictionary<string, string>();
        }

        // Step 2: Create all media items in a single batch request
        var newMediaItems = uploadTokens.Select(token => new
        {
            description = token.Description,
            simpleMediaItem = new
            {
                uploadToken = token.UploadToken
            }
        }).ToArray();

        var createRequest = new HttpRequestMessage(HttpMethod.Post, "https://photoslibrary.googleapis.com/v1/mediaItems:batchCreate");
        createRequest.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
        createRequest.Content = new StringContent(JsonSerializer.Serialize(new
        {
            newMediaItems = newMediaItems
        }), System.Text.Encoding.UTF8, "application/json");

        var createResponse = await _retryPipeline.ExecuteAsync(async _ => await _httpClient.SendAsync(createRequest, cancellationToken));
        createResponse.EnsureSuccessStatusCode();

        // Parse the response to get the created media items with their productUrls
        var responseContent = await createResponse.Content.ReadAsStringAsync(cancellationToken);
        var batchCreateResponse = JsonSerializer.Deserialize<BatchCreateResponse>(responseContent);

        var result = new Dictionary<string, string>();
        var createdMediaItemIds = new List<string>();

        if (batchCreateResponse?.NewMediaItemResults != null)
        {
            for (int i = 0; i < batchCreateResponse.NewMediaItemResults.Count && i < uploadTokens.Count; i++)
            {
                var mediaItemResult = batchCreateResponse.NewMediaItemResults[i];
                var jobId = uploadTokens[i].JobId;

                if (mediaItemResult.MediaItem?.ProductUrl != null)
                {
                    result[jobId] = mediaItemResult.MediaItem.ProductUrl;
                    _logger.LogDebug("Mapped job {JobId} to Google Photos URL: {ProductUrl}", jobId, mediaItemResult.MediaItem.ProductUrl);

                    // Collect media item ID for album addition
                    if (!string.IsNullOrEmpty(mediaItemResult.MediaItem.Id))
                    {
                        createdMediaItemIds.Add(mediaItemResult.MediaItem.Id);
                    }
                }
                else
                {
                    _logger.LogWarning("No productUrl found for job {JobId} in batch upload response", jobId);
                }
            }
        }

        // Step 3: Add uploaded media items to Gallery Tuner album
        if (createdMediaItemIds.Any())
        {
            try
            {
                var albumId = await EnsureGalleryTunerAlbumAsync(accessToken);
                await AddMediaItemsToAlbumAsync(accessToken, albumId, createdMediaItemIds);
                _logger.LogInformation("Successfully added {Count} uploaded items to Gallery Tuner album", createdMediaItemIds.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to add uploaded items to Gallery Tuner album, but upload was successful");
                // Don't throw here - the upload was successful, album addition is a bonus feature
            }
        }

        _logger.LogInformation("Successfully completed batch upload of {ItemCount} items to Google Photos, mapped {MappedCount} URLs",
            uploadTokens.Count, result.Count);

        return result;
    }
}

public class VideoInfo
{
    public string MediaItemId { get; set; } = string.Empty;
    public int Width { get; set; }
    public int Height { get; set; }
    public double Duration { get; set; }
    public long EstimatedSizeBytes { get; set; }
    public string Filename { get; set; } = string.Empty;
}

// JSON models for Google Photos API responses
public class MediaItemsResponse
{
    [JsonPropertyName("mediaItems")]
    public List<MediaItem> MediaItems { get; set; } = new();

    [JsonPropertyName("nextPageToken")]
    public string? NextPageToken { get; set; }
}

public class MediaItem
{
    [JsonPropertyName("id")]
    public string Id { get; set; } = string.Empty;

    [JsonPropertyName("filename")]
    public string Filename { get; set; } = string.Empty;

    [JsonPropertyName("baseUrl")]
    public string BaseUrl { get; set; } = string.Empty;

    [JsonPropertyName("productUrl")]
    public string? ProductUrl { get; set; }

    [JsonPropertyName("mediaMetadata")]
    public MediaMetadata? MediaMetadata { get; set; }
}

public class MediaMetadata
{
    [JsonPropertyName("width")]
    public string Width { get; set; } = string.Empty;

    [JsonPropertyName("height")]
    public string Height { get; set; } = string.Empty;

    [JsonPropertyName("video")]
    public VideoMetadata? Video { get; set; }
}

public class VideoMetadata
{
    [JsonPropertyName("fps")]
    public double Fps { get; set; }

    [JsonPropertyName("processingStatus")]
    public string ProcessingStatus { get; set; } = string.Empty;
}

public class BatchUploadItem
{
    public string Id { get; set; } = string.Empty;
    public string CompressedFilePath { get; set; } = string.Empty;
    public int MediaType { get; set; } // 0 = Photo, 1 = Video
}

public class BatchCreateResponse
{
    [JsonPropertyName("newMediaItemResults")]
    public List<NewMediaItemResult> NewMediaItemResults { get; set; } = new();
}

public class NewMediaItemResult
{
    [JsonPropertyName("uploadToken")]
    public string? UploadToken { get; set; }

    [JsonPropertyName("status")]
    public Status? Status { get; set; }

    [JsonPropertyName("mediaItem")]
    public MediaItem? MediaItem { get; set; }
}

public class Status
{
    [JsonPropertyName("message")]
    public string? Message { get; set; }
}

// Album-related models
public class Album
{
    [JsonPropertyName("id")]
    public string Id { get; set; } = string.Empty;

    [JsonPropertyName("title")]
    public string Title { get; set; } = string.Empty;

    [JsonPropertyName("productUrl")]
    public string ProductUrl { get; set; } = string.Empty;

    [JsonPropertyName("isWriteable")]
    public bool IsWriteable { get; set; }

    [JsonPropertyName("mediaItemsCount")]
    public string MediaItemsCount { get; set; } = string.Empty;

    [JsonPropertyName("coverPhotoBaseUrl")]
    public string CoverPhotoBaseUrl { get; set; } = string.Empty;

    [JsonPropertyName("coverPhotoMediaItemId")]
    public string CoverPhotoMediaItemId { get; set; } = string.Empty;
}

public class ListAlbumsResponse
{
    [JsonPropertyName("albums")]
    public List<Album> Albums { get; set; } = new();

    [JsonPropertyName("nextPageToken")]
    public string? NextPageToken { get; set; }
}

public class CreateAlbumRequest
{
    [JsonPropertyName("album")]
    public AlbumDetails Album { get; set; } = new();
}

public class AlbumDetails
{
    [JsonPropertyName("title")]
    public string Title { get; set; } = string.Empty;
}

public class BatchAddMediaItemsRequest
{
    [JsonPropertyName("mediaItemIds")]
    public List<string> MediaItemIds { get; set; } = new();
}
