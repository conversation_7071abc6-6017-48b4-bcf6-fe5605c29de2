### Test Album Functionality
### This file contains HTTP requests to test the new Gallery Tuner album functionality

@backend_url = http://localhost:5119
@jwt_token = YOUR_JWT_TOKEN_HERE

### 1. Test uploading a compressed photo (should create/add to Gallery Tuner album)
POST {{backend_url}}/api/compression/batch-upload
Authorization: Bearer {{jwt_token}}
Content-Type: application/json

{
  "jobIds": ["test-job-id-1"]
}

### 2. Test uploading multiple compressed items (should add all to Gallery Tuner album)
POST {{backend_url}}/api/compression/batch-upload
Authorization: Bearer {{jwt_token}}
Content-Type: application/json

{
  "jobIds": ["test-job-id-1", "test-job-id-2", "test-job-id-3"]
}

### 3. Check Google Photos API directly to verify album exists
### Note: This would need to be done manually in Google Photos web interface
### or through a separate test endpoint that lists albums

### 4. Test individual photo upload (should also add to album)
### This would be triggered through the normal compression workflow
### when UploadToGooglePhotos is enabled

### 5. Test individual video upload (should also add to album)
### This would be triggered through the normal compression workflow
### when UploadToGooglePhotos is enabled
